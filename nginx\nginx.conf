server {
    listen 80;
    server_name yourdomain.com;  # Replace with your actual domain

    # Frontend static files
    location / {
        root /var/www/html;  # Your React build directory
        try_files $uri $uri/ /index.html;
    }

    # Backend API proxy
    location /api/ {
        proxy_pass http://localhost:8000/;  # Your FastAPI backend
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Allow all HTTP methods
        proxy_method $request_method;
        proxy_pass_request_headers on;
        proxy_pass_request_body on;
    }

    # Uploads directory
    location /uploads/ {
        alias /path/to/your/uploads/;  # Replace with your actual uploads directory
        try_files $uri =404;
    }
} 