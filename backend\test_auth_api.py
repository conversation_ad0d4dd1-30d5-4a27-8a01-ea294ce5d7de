#!/usr/bin/env python3
"""
Test script to verify auth API endpoints are working correctly.
"""

import requests
import json

BASE_URL = "http://localhost:8000"

def test_signin():
    """Test signin endpoint"""
    print("Testing signin...")
    response = requests.post(f"{BASE_URL}/api/auth/signin", json={
        "email": "<EMAIL>",
        "password": "admin123"
    })
    
    if response.status_code == 200:
        data = response.json()
        print("✅ Signin successful")
        print(f"Token: {data['access_token'][:50]}...")
        print(f"Role: {data['role']}")
        return data['access_token']
    else:
        print(f"❌ Signin failed: {response.status_code} - {response.text}")
        return None

def test_list_users(token):
    """Test list users endpoint"""
    print("\nTesting list users...")
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/auth/users", headers=headers)
    
    if response.status_code == 200:
        users = response.json()
        print(f"✅ List users successful - Found {len(users)} users")
        for user in users:
            print(f"  - {user['first_name']} {user['last_name']} ({user['email']}) - {user['role']}")
        return users
    else:
        print(f"❌ List users failed: {response.status_code} - {response.text}")
        return []

def test_create_user(token):
    """Test create user endpoint"""
    print("\nTesting create user...")
    headers = {"Authorization": f"Bearer {token}"}
    user_data = {
        "first_name": "Test",
        "last_name": "Admin",
        "email": "<EMAIL>",
        "password": "test123",
        "role": "admin"
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/users", json=user_data, headers=headers)
    
    if response.status_code == 200:
        user = response.json()
        print(f"✅ Create user successful - ID: {user['id']}")
        return user['id']
    else:
        print(f"❌ Create user failed: {response.status_code} - {response.text}")
        return None

def test_update_user(token, user_id):
    """Test update user endpoint"""
    print(f"\nTesting update user {user_id}...")
    headers = {"Authorization": f"Bearer {token}"}
    update_data = {
        "first_name": "Updated Test",
        "last_name": "Admin Updated"
    }
    
    response = requests.put(f"{BASE_URL}/api/auth/users/{user_id}", json=update_data, headers=headers)
    
    if response.status_code == 200:
        user = response.json()
        print(f"✅ Update user successful - Name: {user['first_name']} {user['last_name']}")
        return True
    else:
        print(f"❌ Update user failed: {response.status_code} - {response.text}")
        return False

def test_delete_user(token, user_id):
    """Test delete user endpoint"""
    print(f"\nTesting delete user {user_id}...")
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.delete(f"{BASE_URL}/api/auth/users/{user_id}", headers=headers)
    
    if response.status_code == 200:
        print("✅ Delete user successful")
        return True
    else:
        print(f"❌ Delete user failed: {response.status_code} - {response.text}")
        return False

def main():
    print("🚀 Testing Auth API endpoints...")
    
    # Test signin
    token = test_signin()
    if not token:
        print("❌ Cannot proceed without valid token")
        return
    
    # Test list users
    users = test_list_users(token)
    
    # Test create user
    user_id = test_create_user(token)
    if user_id:
        # Test update user
        test_update_user(token, user_id)
        
        # Test delete user
        test_delete_user(token, user_id)
    
    print("\n🎉 API testing completed!")

if __name__ == "__main__":
    main()