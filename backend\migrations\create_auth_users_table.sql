-- Create auth_users table for authentication and roles
CREATE TABLE IF NOT EXISTS auth_users (
    id SERIAL PRIMARY KEY,
    first_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    last_name VA<PERSON>HAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('super_admin', 'admin', 'user')),
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- Seed super admin user if not exists
INSERT INTO auth_users (first_name, last_name, email, password_hash, role)
SELECT 'Super', 'Admin', '<EMAIL>', '$2b$12$PLACEHOLDERHASH', 'super_admin'
WHERE NOT EXISTS (
    SELECT 1 FROM auth_users WHERE role = 'super_admin'
); 