import React, { useState, Suspense, useEffect, useRef, useLayoutEffect } from 'react';
import { Layout, Menu, Button, Typography, Spin, Avatar, Drawer } from 'antd';
import {
  SettingOutlined,
  HomeOutlined,
  UserOutlined,
  TeamOutlined,
  BankOutlined,
  EnvironmentOutlined,
  DollarOutlined,
  ShopOutlined,
  DatabaseOutlined,
  PercentageOutlined,
  ContactsOutlined,
  MailOutlined,
  MenuOutlined
} from '@ant-design/icons';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
// Import logo from assets
import logo from '../../assets/logo.png';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

const MainLayout: React.FC = () => {
  const [settingsSidebarVisible, setSettingsSidebarVisible] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const navBarRef = useRef<HTMLDivElement>(null);
  const [underlineStyle, setUnderlineStyle] = useState<{ left: number; width: number }>({ left: 0, width: 0 });
  const [mobileNavOpen, setMobileNavOpen] = useState(false);
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);

  // Check if current route is a settings page
  useEffect(() => {
    setSettingsSidebarVisible(location.pathname.startsWith('/settings'));
  }, [location.pathname]);

  // Get user info from localStorage
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const role = localStorage.getItem('role');

  const handleSignOut = () => {
    localStorage.clear();
    navigate('/signin');
  };

  const mainMenuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: 'Home',
    },
    {
      key: '/customers',
      icon: <ContactsOutlined />,
      label: 'Customers',
    },
    {
      key: '/suppliers',
      icon: <ShopOutlined />,
      label: 'Suppliers',
    },
    {
      key: '/accounts',
      icon: <DollarOutlined />,
      label: 'Accounts',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: 'Settings',
    },
  ];

  useLayoutEffect(() => {
    function updateUnderline() {
      const navBar = navBarRef.current;
      if (!navBar) return;
      const tabEls = navBar.querySelectorAll('.ant-menu-item');
      if (tabEls.length === 0) return;
      const firstTab = tabEls[0] as HTMLElement;
      const lastTab = tabEls[tabEls.length - 1 ] as HTMLElement;
      const left = firstTab.offsetLeft;
      const width = (lastTab.offsetLeft + lastTab.offsetWidth) ;
      setUnderlineStyle({ left, width });
    }
    updateUnderline();
    window.addEventListener('resize', updateUnderline);
    return () => window.removeEventListener('resize', updateUnderline);
  }, [mainMenuItems.length]);

  const settingsMenuItems = [
    {
      key: '/settings/chart-of-accounts',
      icon: <BankOutlined />,
      label: 'Chart of Account',
    },
    {
      key: '/settings/departments',
      icon: <TeamOutlined />,
      label: 'Department',
    },
    {
      key: '/settings/locations',
      icon: <EnvironmentOutlined />,
      label: 'Location',
    },
    {
      key: '/settings/currencies',
      icon: <DollarOutlined />,
      label: 'Currency',
    },
    {
      key: '/settings/manufacturers',
      icon: <ShopOutlined />,
      label: 'Manufacturers',
    },
    {
      key: '/settings/users',
      icon: <UserOutlined />,
      label: 'Users',
    },
    {
      key: '/settings/teams',
      icon: <TeamOutlined />,
      label: 'Teams',
    },
    {
      key: '/settings/warehouses',
      icon: <DatabaseOutlined />,
      label: 'Inventory Management',
    },
    {
      key: '/settings/commissions',
      icon: <PercentageOutlined />,
      label: 'Commissions',
    },
  ];

  // Optionally filter Users menu item by role
  const filteredSettingsMenuItems = settingsMenuItems.filter(item => {
    if (item.key === '/settings/users') {
      return role === 'admin' || role === 'super_admin';
    }
    return true;
  });

  // Add Auth User Management link for admin/super_admin
  if (role === 'admin' || role === 'super_admin') {
    filteredSettingsMenuItems.unshift({
      key: '/settings/auth-users',
      icon: <UserOutlined />,
      label: 'Auth User Management',
    });
  }

  const handleMenuClick = (e: any) => {
    if (e.key === '/settings') {
      navigate('/settings/users');
    } else {
      navigate(e.key);
    }
  };

  const getCurrentSettingsMenuItem = () => {
    return settingsMenuItems.find(item => location.pathname === item.key)?.key || '/settings/users';
  };

  return (
    <Layout className="min-h-screen" style={{ marginLeft: 10 }} >
      <Header className="bg-white px-2 sm:px-6 shadow-sm flex flex-col sm:flex-row items-center justify-between h-auto sm:h-16 z-10 border-b border-gray-200">
        <div className="flex items-center w-full sm:w-auto justify-between gap-2 sm:gap-6">
          <div className="flex items-center gap-2 sm:gap-4">
            <div className="logo-container mr-2 sm:mr-4">
              <img 
                src={logo} 
                alt="Company Logo" 
                className="h-8 sm:h-10 w-auto"
              />
            </div>
            <div className="h-8 sm:h-10 border-l border-gray-300 mx-2 sm:mx-4" style={{ minWidth: 1 }} />
            <Title level={4} className="m-0 hidden sm:block text-lg sm:text-xl font-semibold tracking-tight text-gray-800">
              Client Centre
            </Title>
          </div>
          <Button
            type="text"
            icon={<MenuOutlined style={{ fontSize: 24 }} />}
            className="sm:hidden"
            onClick={() => setMobileNavOpen(true)}
          />
        </div>
        <Title level={4} className="m-0 block sm:hidden mt-2 text-lg font-semibold text-gray-800">
          Client Centre
        </Title>
        <div className="flex items-center mt-2 sm:mt-0 gap-2 sm:gap-4">
          <Button
            type="text"
            icon={<MailOutlined />}
            className="mr-1 sm:mr-2"
          />
          <div className="user-info flex items-center cursor-pointer gap-2">
            <span className="text-sm font-medium text-gray-700">{user.first_name} {user.last_name} ({role})</span>
            <Avatar icon={<UserOutlined />} size={32} className="bg-blue-100 text-blue-700" />
            <Button type="link" onClick={handleSignOut} style={{ marginLeft: 8 }}>
              Sign Out
            </Button>
          </div>
        </div>
      </Header>
      
      <div className="nav-bar-wrapper relative">
        <div className="top-navigation bg-white hidden sm:block" ref={navBarRef}>
          <Menu
            mode="horizontal"
            selectedKeys={[location.pathname.split('/')[1] ? '/' + location.pathname.split('/')[1] : '/']}
            items={mainMenuItems}
            onClick={handleMenuClick}
            className="border-r-0"
          />
          <div
            className="custom-nav-underline absolute hidden sm:block"
            style={{ left: underlineStyle.left, width: underlineStyle.width, top: 49, height: 2, background: '#222', zIndex: 1, pointerEvents: 'none' }}
          />
        </div>
        <Drawer
          title="Menu"
          placement="left"
          onClose={() => setMobileNavOpen(false)}
          open={mobileNavOpen}
          className="sm:hidden"
          bodyStyle={{ padding: 0 }}
        >
          <Menu
            mode="vertical"
            selectedKeys={[location.pathname.split('/')[1] ? '/' + location.pathname.split('/')[1] : '/']}
            items={mainMenuItems}
            onClick={e => { setMobileNavOpen(false); handleMenuClick(e); }}
          />
        </Drawer>
      </div>
      
      <Layout>
        {settingsSidebarVisible && (
          <>
            <Sider
              width={270}
              className="bg-white border-0 shadow-none hidden sm:block"
              style={{ background: '#fff', boxShadow: 'none', border: 0 }}
              collapsible={false}
            >
              <div className="custom-sidebar">
                <div className="custom-sidebar-header">System settings</div>
                <ul className="custom-sidebar-list">
                  {filteredSettingsMenuItems.map(item => (
                    <li
                      key={item.key}
                      className={
                        'custom-sidebar-item' +
                        (getCurrentSettingsMenuItem() === item.key ? ' selected' : '')
                      }
                      onClick={() => handleMenuClick({ key: item.key })}
                    >
                      {item.icon && <span style={{ fontSize: 18, color: getCurrentSettingsMenuItem() === item.key ? '#1976d2' : '#b0b0b0' }}>{item.icon}</span>}
                      <span>{item.label}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </Sider>
            <Drawer
              title="System settings"
              placement="left"
              onClose={() => setMobileSidebarOpen(false)}
              open={mobileSidebarOpen}
              className="sm:hidden"
              bodyStyle={{ padding: 0 }}
            >
              <ul className="custom-sidebar-list">
                {filteredSettingsMenuItems.map(item => (
                  <li
                    key={item.key}
                    className={
                      'custom-sidebar-item' +
                      (getCurrentSettingsMenuItem() === item.key ? ' selected' : '')
                    }
                    onClick={() => { setMobileSidebarOpen(false); handleMenuClick({ key: item.key }); }}
                  >
                    {item.icon && <span style={{ fontSize: 18, color: getCurrentSettingsMenuItem() === item.key ? '#1976d2' : '#b0b0b0' }}>{item.icon}</span>}
                    <span>{item.label}</span>
                  </li>
                ))}
              </ul>
            </Drawer>
          </>
        )}
        
        <Content className="p-2 sm:p-6 bg-white min-h-[calc(100vh-64px-46px)]">
          {settingsSidebarVisible && (
            <Button
              type="primary"
              className="mb-4 sm:hidden"
              onClick={() => setMobileSidebarOpen(true)}
            >
              Open Settings Menu
            </Button>
          )}
          <Suspense fallback={
            <div className="flex justify-center items-center h-64">
              <Spin size="large" />
            </div>
          }>
            <Outlet />
          </Suspense>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
