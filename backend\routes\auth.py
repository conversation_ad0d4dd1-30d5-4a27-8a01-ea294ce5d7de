from fastapi import APIRouter, HTTPException, status, Depends, Request
from pydantic import BaseModel
import psycopg2
import os
import jwt  # Placeholder, use PyJWT
import bcrypt  # Placeholder, use bcrypt
from dotenv import load_dotenv
from datetime import datetime, timedelta
from models import AuthUser
from typing import List

load_dotenv()

router = APIRouter()

DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'database': os.getenv('DB_NAME', 'postgres'),
    'user': os.getenv('DB_USER', 'postgres'),
    'password': os.getenv('DB_PASSWORD', ''),
    'port': int(os.getenv('DB_PORT', 5432))
}

JWT_SECRET = os.getenv('JWT_SECRET', 'supersecret')
JWT_ALGORITHM = 'HS256'
JWT_EXPIRATION_MINUTES = 60

class SignInRequest(BaseModel):
    email: str
    password: str

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    role: str
    first_name: str
    last_name: str
    email: str

@router.post("/auth/signin", response_model=TokenResponse)
def signin(data: SignInRequest):
    try:
        connection = psycopg2.connect(**DB_CONFIG)
        cursor = connection.cursor()
        cursor.execute(
            "SELECT id, first_name, last_name, email, password_hash, role FROM auth_users WHERE email = %s",
            (data.email,)
        )
        user = cursor.fetchone()
        if not user:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")
        user_id, first_name, last_name, email, password_hash, role = user
        # Password check (placeholder, use bcrypt)
        if not bcrypt.checkpw(data.password.encode(), password_hash.encode()):
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")
        # Generate JWT (placeholder)
        payload = {
            "sub": str(user_id),
            "role": role,
            "exp": datetime.utcnow() + timedelta(minutes=JWT_EXPIRATION_MINUTES)
        }
        token = jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)
        return TokenResponse(
            access_token=token,
            role=role,
            first_name=first_name,
            last_name=last_name,
            email=email
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

def get_jwt_payload(request: Request):
    """Extract and decode JWT from Authorization header."""
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        raise HTTPException(status_code=401, detail='Missing or invalid Authorization header')
    token = auth_header.split(' ')[1]
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return payload
    except Exception:
        raise HTTPException(status_code=401, detail='Invalid or expired token')

def require_admin(payload=Depends(get_jwt_payload)):
    if payload['role'] not in ['admin', 'super_admin']:
        raise HTTPException(status_code=403, detail='Insufficient permissions')
    return payload

@router.get("/auth/users", response_model=List[AuthUser])
def list_auth_users(dep=Depends(require_admin)):
    try:
        connection = psycopg2.connect(**DB_CONFIG)
        cursor = connection.cursor()
        cursor.execute("SELECT id, first_name, last_name, email, role, created_at, updated_at FROM auth_users")
        users = cursor.fetchall()
        return [AuthUser(
            id=u[0], first_name=u[1], last_name=u[2], email=u[3], role=u[4], created_at=str(u[5]), updated_at=str(u[6])
        ) for u in users]
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@router.get("/auth/users/{user_id}", response_model=AuthUser)
def get_auth_user(user_id: int, dep=Depends(require_admin)):
    try:
        connection = psycopg2.connect(**DB_CONFIG)
        cursor = connection.cursor()
        cursor.execute("SELECT id, first_name, last_name, email, role, created_at, updated_at FROM auth_users WHERE id = %s", (user_id,))
        u = cursor.fetchone()
        if not u:
            raise HTTPException(status_code=404, detail='User not found')
        return AuthUser(id=u[0], first_name=u[1], last_name=u[2], email=u[3], role=u[4], created_at=str(u[5]), updated_at=str(u[6]))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

class AuthUserCreate(BaseModel):
    first_name: str
    last_name: str
    email: str
    password: str
    role: str

@router.post("/auth/users", response_model=AuthUser)
def create_auth_user(data: AuthUserCreate, dep=Depends(require_admin)):
    connection = None
    cursor = None
    try:
        connection = psycopg2.connect(**DB_CONFIG)
        cursor = connection.cursor()
        # Hash password
        hashed = bcrypt.hashpw(data.password.encode(), bcrypt.gensalt()).decode()
        cursor.execute(
            "INSERT INTO auth_users (first_name, last_name, email, password_hash, role) VALUES (%s, %s, %s, %s, %s) RETURNING id, created_at, updated_at",
            (data.first_name, data.last_name, data.email, hashed, data.role)
        )
        user_id, created_at, updated_at = cursor.fetchone()
        connection.commit()
        return AuthUser(id=user_id, first_name=data.first_name, last_name=data.last_name, email=data.email, role=data.role, created_at=str(created_at), updated_at=str(updated_at))
    except psycopg2.IntegrityError as e:
        if connection:
            connection.rollback()
        if "unique constraint" in str(e).lower() and "email" in str(e).lower():
            raise HTTPException(status_code=400, detail="Email already exists")
        raise HTTPException(status_code=400, detail="Database constraint violation")
    except Exception as e:
        if connection:
            connection.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

class AuthUserUpdate(BaseModel):
    first_name: str | None = None
    last_name: str | None = None
    email: str | None = None
    password: str | None = None
    role: str | None = None

@router.put("/auth/users/{user_id}", response_model=AuthUser)
def update_auth_user(user_id: int, data: AuthUserUpdate, dep=Depends(require_admin)):
    connection = None
    cursor = None
    try:
        connection = psycopg2.connect(**DB_CONFIG)
        cursor = connection.cursor()
        # Fetch current user
        cursor.execute("SELECT id, first_name, last_name, email, role, created_at, updated_at FROM auth_users WHERE id = %s", (user_id,))
        u = cursor.fetchone()
        if not u:
            raise HTTPException(status_code=404, detail='User not found')
        target_role = u[4]
        # Only allow super admin to edit itself
        if target_role == 'super_admin':
            if dep['role'] != 'super_admin' or int(dep['sub']) != user_id:
                raise HTTPException(status_code=403, detail='Only the super admin can edit their own account')
        update_fields = []
        update_values = []
        if data.first_name is not None:
            update_fields.append('first_name = %s')
            update_values.append(data.first_name)
        if data.last_name is not None:
            update_fields.append('last_name = %s')
            update_values.append(data.last_name)
        if data.email is not None:
            update_fields.append('email = %s')
            update_values.append(data.email)
        if data.role is not None:
            update_fields.append('role = %s')
            update_values.append(data.role)
        if data.password is not None and data.password.strip():
            hashed = bcrypt.hashpw(data.password.encode(), bcrypt.gensalt()).decode()
            update_fields.append('password_hash = %s')
            update_values.append(hashed)
        if not update_fields:
            raise HTTPException(status_code=400, detail='No fields to update')
        update_fields.append('updated_at = NOW()')
        query = f"UPDATE auth_users SET {', '.join(update_fields)} WHERE id = %s RETURNING id, first_name, last_name, email, role, created_at, updated_at"
        update_values.append(user_id)
        cursor.execute(query, tuple(update_values))
        updated = cursor.fetchone()
        if not updated:
            raise HTTPException(status_code=404, detail='User not found after update')
        connection.commit()
        return AuthUser(id=updated[0], first_name=updated[1], last_name=updated[2], email=updated[3], role=updated[4], created_at=str(updated[5]), updated_at=str(updated[6]))
    except psycopg2.IntegrityError as e:
        if connection:
            connection.rollback()
        if "unique constraint" in str(e).lower() and "email" in str(e).lower():
            raise HTTPException(status_code=400, detail="Email already exists")
        raise HTTPException(status_code=400, detail="Database constraint violation")
    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        if connection:
            connection.rollback()
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

@router.delete("/auth/users/{user_id}")
def delete_auth_user(user_id: int, dep=Depends(require_admin)):
    try:
        connection = psycopg2.connect(**DB_CONFIG)
        cursor = connection.cursor()
        # Prevent deleting super admin
        cursor.execute("SELECT role FROM auth_users WHERE id = %s", (user_id,))
        row = cursor.fetchone()
        if not row:
            raise HTTPException(status_code=404, detail='User not found')
        if row[0] == 'super_admin':
            raise HTTPException(status_code=403, detail='Cannot delete super admin')
        cursor.execute("DELETE FROM auth_users WHERE id = %s", (user_id,))
        connection.commit()
        return {"detail": "User deleted"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close() 