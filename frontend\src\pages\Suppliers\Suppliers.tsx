import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Typography,
  Space,
  Spin,
  Drawer
} from 'antd';
import { PlusOutlined, ShopOutlined } from '@ant-design/icons';
import { apiService } from '../../api';
import type { Supplier } from '../../api';
import { useErrorHandler } from '../../hooks/useErrorHandler';
import ErrorToast from '../../components/ErrorDisplay/ErrorToast';
import SupplierInfo from './SupplierInfo';
import AddSupplierModal from './AddSupplierModal';

const { Title } = Typography;

const Suppliers: React.FC = () => {
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);
  const [loading, setLoading] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);

  // Use our custom error handler hook
  const { errorMessage, showError, clearError } = useErrorHandler();

  const fetchSuppliers = async () => {
    setLoading(true);
    try {
      const response = await apiService.getSuppliers();
      setSuppliers(response.suppliers);
      if (response.suppliers.length > 0 && !selectedSupplier) {
        setSelectedSupplier(response.suppliers[0]);
      }
    } catch (error: any) {
      showError(error.message || 'Failed to fetch suppliers');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSuppliers();
  }, []);

  const handleSupplierSelect = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
  };

  const handleSupplierUpdate = async () => {
    await fetchSuppliers(); // Refresh supplier list after updates
    // Update selected supplier with fresh data
    if (selectedSupplier) {
      const updatedSuppliers = await apiService.getSuppliers();
      const updatedSelectedSupplier = updatedSuppliers.suppliers.find((s: Supplier) => s.id === selectedSupplier.id);
      if (updatedSelectedSupplier) {
        setSelectedSupplier(updatedSelectedSupplier);
      }
    }
  };

  const handleAddSupplier = () => {
    setShowAddModal(true);
  };

  const handleAddSupplierSuccess = (newSupplier: Supplier) => {
    setShowAddModal(false);
    fetchSuppliers();
    setSelectedSupplier(newSupplier);
  };

  const handleSupplierDelete = () => {
    setSelectedSupplier(null);
    fetchSuppliers();
  };

  if (loading && suppliers.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <Title level={2}>Supplier Management</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddSupplier}
        >
          Add Supplier
        </Button>
      </div>

      {/* Error Display */}
      <ErrorToast message={errorMessage} onClose={clearError} />

      <div className="grid grid-cols-1 md:grid-cols-12 gap-2 md:gap-6">
        {/* Supplier List Sidebar */}
        <div className="hidden md:block md:col-span-3 col-span-1">
          <div className="custom-sidebar">
            <div className="custom-sidebar-header">Suppliers</div>
            <ul className="custom-sidebar-list">
              {suppliers.map((supplier) => (
                <li
                  key={supplier.id}
                  className={
                    'custom-sidebar-item' +
                    (selectedSupplier?.id === supplier.id ? ' selected' : '')
                  }
                  onClick={() => handleSupplierSelect(supplier)}
                >
                  <ShopOutlined style={{ fontSize: 18, color: selectedSupplier?.id === supplier.id ? '#1976d2' : '#b0b0b0' }} />
                  <span>
                    <div className="font-medium text-sm">{supplier.name}</div>
                    <div className="text-xs text-gray-500">{supplier.category || 'No category'}</div>
                  </span>
                </li>
              ))}
              {suppliers.length === 0 && (
                <li className="custom-sidebar-item text-center text-gray-500 py-4">
                  No suppliers found
                </li>
              )}
            </ul>
          </div>
        </div>
        {/* Mobile Sidebar Drawer */}
        <Drawer
          title="Suppliers"
          placement="left"
          onClose={() => setMobileSidebarOpen(false)}
          open={mobileSidebarOpen}
          className="md:hidden"
          bodyStyle={{ padding: 0 }}
        >
          <ul className="custom-sidebar-list">
            {suppliers.map((supplier) => (
              <li
                key={supplier.id}
                className={
                  'custom-sidebar-item' +
                  (selectedSupplier?.id === supplier.id ? ' selected' : '')
                }
                onClick={() => { setMobileSidebarOpen(false); handleSupplierSelect(supplier); }}
              >
                <ShopOutlined style={{ fontSize: 18, color: selectedSupplier?.id === supplier.id ? '#1976d2' : '#b0b0b0' }} />
                <span>
                  <div className="font-medium text-sm">{supplier.name}</div>
                  <div className="text-xs text-gray-500">{supplier.category || 'No category'}</div>
                </span>
              </li>
            ))}
            {suppliers.length === 0 && (
              <li className="custom-sidebar-item text-center text-gray-500 py-4">
                No suppliers found
              </li>
            )}
          </ul>
        </Drawer>
        {/* Sidebar open button for mobile */}
        <div className="md:hidden col-span-1 mb-2">
          <Button type="primary" onClick={() => setMobileSidebarOpen(true)}>
            Open Suppliers Menu
          </Button>
        </div>
        {/* Supplier Details */}
        <div className="md:col-span-9 col-span-1 mt-6 md:mt-0">
          {selectedSupplier ? (
            <Card>
              <div className="mb-4">
                <Title level={3} className="mb-2">{selectedSupplier.name}</Title>
                <div className="text-gray-600">
                  <Space>
                    <span>{selectedSupplier.category || 'No category'}</span>
                    {selectedSupplier.sales_rep_name && (
                      <>
                        <span>•</span>
                        <span>Sales Rep: {selectedSupplier.sales_rep_name}</span>
                      </>
                    )}
                  </Space>
                </div>
              </div>

              <SupplierInfo
                supplier={selectedSupplier}
                onUpdate={handleSupplierUpdate}
                onDelete={handleSupplierDelete}
              />
            </Card>
          ) : (
            <Card>
              <div className="text-center py-12">
                <ShopOutlined className="text-4xl text-gray-300 mb-4" />
                <Title level={4} className="text-gray-500">Select a supplier to view details</Title>
                <p className="text-gray-400">Choose a supplier from the list to see their information.</p>
              </div>
            </Card>
          )}
        </div>
      </div>

      {/* Add Supplier Modal */}
      <AddSupplierModal
        visible={showAddModal}
        onCancel={() => setShowAddModal(false)}
        onSuccess={handleAddSupplierSuccess}
      />
    </div>
  );
};

export default Suppliers;
