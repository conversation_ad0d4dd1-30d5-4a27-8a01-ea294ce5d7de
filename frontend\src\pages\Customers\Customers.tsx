import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Typography,
  Space,
  Spin,
  Tabs,
  Drawer
} from 'antd';
import { PlusOutlined, UserOutlined } from '@ant-design/icons';
import { apiService } from '../../api';
import type { Customer } from '../../api';
import { useErrorHandler } from '../../hooks/useErrorHandler';
import ErrorToast from '../../components/ErrorDisplay/ErrorToast';
import CustomerInfo from './CustomerInfo';
import CustomerQuotes from './CustomerQuotes';
import CustomerAccounts from './CustomerAccounts';
import CustomerProjects from './CustomerProjects';
import AddCustomerModal from './AddCustomerModal';

const { Title } = Typography;
const { TabPane } = Tabs;

const Customers: React.FC = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);

  // Use our custom error handler hook
  const { errorMessage, showError, clearError } = useErrorHandler();

  const fetchCustomers = async () => {
    setLoading(true);
    try {
      const response = await apiService.getCustomers();
      setCustomers(response.customers);
      if (response.customers.length > 0 && !selectedCustomer) {
        setSelectedCustomer(response.customers[0]);
      }
    } catch (error: any) {
      showError(error.message || 'Failed to fetch customers');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCustomers();
  }, []);

  const handleCustomerSelect = (customer: Customer) => {
    setSelectedCustomer(customer);
  };

  const handleCustomerUpdate = async () => {
    await fetchCustomers(); // Refresh customer list after updates
    // Update selected customer with fresh data
    if (selectedCustomer) {
      const updatedCustomers = await apiService.getCustomers();
      const updatedSelectedCustomer = updatedCustomers.customers.find((c: Customer) => c.id === selectedCustomer.id);
      if (updatedSelectedCustomer) {
        setSelectedCustomer(updatedSelectedCustomer);
      }
    }
  };

  const handleAddCustomer = () => {
    setShowAddModal(true);
  };

  const handleAddCustomerSuccess = (newCustomer: Customer) => {
    setShowAddModal(false);
    fetchCustomers();
    setSelectedCustomer(newCustomer);
  };

  const handleCustomerDelete = () => {
    setSelectedCustomer(null);
    fetchCustomers();
  };

  if (loading && customers.length === 0) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <Title level={2}>Customer Management</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddCustomer}
        >
          Add Customer
        </Button>
      </div>

      {/* Error Display */}
      <ErrorToast message={errorMessage} onClose={clearError} />

      <div className="grid grid-cols-1 md:grid-cols-12 gap-2 md:gap-6">
        {/* Customer List Sidebar */}
        <div className="hidden md:block md:col-span-3 col-span-1">
          <div className="custom-sidebar">
            <div className="custom-sidebar-header">Customers</div>
            <ul className="custom-sidebar-list">
              {customers.map((customer) => (
                <li
                  key={customer.id}
                  className={
                    'custom-sidebar-item' +
                    (selectedCustomer?.id === customer.id ? ' selected' : '')
                  }
                  onClick={() => handleCustomerSelect(customer)}
                >
                  <UserOutlined style={{ fontSize: 18, color: selectedCustomer?.id === customer.id ? '#1976d2' : '#b0b0b0' }} />
                  <span>
                    <div className="font-medium text-sm">{customer.name}</div>
                    <div className="text-xs text-gray-500">{customer.category || 'No category'}</div>
                  </span>
                </li>
              ))}
              {customers.length === 0 && (
                <li className="custom-sidebar-item text-center text-gray-500 py-4">
                  No customers found
                </li>
              )}
            </ul>
          </div>
        </div>
        {/* Mobile Sidebar Drawer */}
        <Drawer
          title="Customers"
          placement="left"
          onClose={() => setMobileSidebarOpen(false)}
          open={mobileSidebarOpen}
          className="md:hidden"
          bodyStyle={{ padding: 0 }}
        >
          <ul className="custom-sidebar-list">
            {customers.map((customer) => (
              <li
                key={customer.id}
                className={
                  'custom-sidebar-item' +
                  (selectedCustomer?.id === customer.id ? ' selected' : '')
                }
                onClick={() => { setMobileSidebarOpen(false); handleCustomerSelect(customer); }}
              >
                <UserOutlined style={{ fontSize: 18, color: selectedCustomer?.id === customer.id ? '#1976d2' : '#b0b0b0' }} />
                <span>
                  <div className="font-medium text-sm">{customer.name}</div>
                  <div className="text-xs text-gray-500">{customer.category || 'No category'}</div>
                </span>
              </li>
            ))}
            {customers.length === 0 && (
              <li className="custom-sidebar-item text-center text-gray-500 py-4">
                No customers found
              </li>
            )}
          </ul>
        </Drawer>
        {/* Sidebar open button for mobile */}
        <div className="md:hidden col-span-1 mb-2">
          <Button type="primary" onClick={() => setMobileSidebarOpen(true)}>
            Open Customers Menu
          </Button>
        </div>
        {/* Customer Details */}
        <div className="md:col-span-9 col-span-1 mt-6 md:mt-0">
          {selectedCustomer ? (
            <Card>
              <div className="mb-4">
                <Title level={3} className="mb-2">{selectedCustomer.name}</Title>
                <div className="text-gray-600">
                  <Space>
                    <span>{selectedCustomer.category || 'No category'}</span>
                    {selectedCustomer.sales_rep_name && (
                      <>
                        <span>•</span>
                        <span>Sales Rep: {selectedCustomer.sales_rep_name}</span>
                      </>
                    )}
                  </Space>
                </div>
              </div>

              <Tabs defaultActiveKey="info">
                <TabPane tab="Information" key="info">
                  <CustomerInfo
                    customer={selectedCustomer}
                    onUpdate={handleCustomerUpdate}
                    onDelete={handleCustomerDelete}
                  />
                </TabPane>
                <TabPane tab="Quotes" key="quotes">
                  <CustomerQuotes customerId={selectedCustomer.id} />
                </TabPane>
                <TabPane tab="Accounts" key="accounts">
                  <CustomerAccounts customerId={selectedCustomer.id} />
                </TabPane>
                <TabPane tab="Projects" key="projects">
                  <CustomerProjects customerId={selectedCustomer.id} />
                </TabPane>
              </Tabs>
            </Card>
          ) : (
            <Card>
              <div className="text-center py-12">
                <UserOutlined className="text-4xl text-gray-300 mb-4" />
                <Title level={4} className="text-gray-500">Select a customer to view details</Title>
                <p className="text-gray-400">Choose a customer from the list to see their information.</p>
              </div>
            </Card>
          )}
        </div>
      </div>

      {/* Add Customer Modal */}
      <AddCustomerModal
        visible={showAddModal}
        onCancel={() => setShowAddModal(false)}
        onSuccess={handleAddCustomerSuccess}
      />
    </div>
  );
};

export default Customers;
