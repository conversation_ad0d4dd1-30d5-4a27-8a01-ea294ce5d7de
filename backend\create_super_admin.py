#!/usr/bin/env python3
"""
Script to create a super admin user with a proper password hash.
"""

import psycopg2
import bcrypt
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'database': os.getenv('DB_NAME', 'postgres'),
    'user': os.getenv('DB_USER', 'postgres'),
    'password': os.getenv('DB_PASSWORD', ''),
    'port': int(os.getenv('DB_PORT', 5432))
}

def create_super_admin():
    """Create a super admin user with proper password hash."""
    try:
        print("Connecting to database...")
        connection = psycopg2.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # Check if super admin already exists
        cursor.execute("SELECT id FROM auth_users WHERE role = 'super_admin'")
        existing_admin = cursor.fetchone()
        
        if existing_admin:
            print("Super admin already exists. Updating password...")
            # Update existing super admin with proper password
            password = "admin123"  # Default password - should be changed after first login
            hashed_password = bcrypt.hashpw(password.encode(), bcrypt.gensalt()).decode()
            
            cursor.execute(
                "UPDATE auth_users SET password_hash = %s WHERE role = 'super_admin'",
                (hashed_password,)
            )
            print(f"✅ Super admin password updated. Email: <EMAIL>, Password: {password}")
        else:
            print("Creating new super admin...")
            # Create new super admin
            password = "admin123"  # Default password - should be changed after first login
            hashed_password = bcrypt.hashpw(password.encode(), bcrypt.gensalt()).decode()
            
            cursor.execute(
                """INSERT INTO auth_users (first_name, last_name, email, password_hash, role) 
                   VALUES (%s, %s, %s, %s, %s)""",
                ('Super', 'Admin', '<EMAIL>', hashed_password, 'super_admin')
            )
            print(f"✅ Super admin created. Email: <EMAIL>, Password: {password}")
        
        connection.commit()
        print("✅ Super admin setup completed successfully!")
        print("\n⚠️  IMPORTANT: Please change the default password after first login!")
        
    except Exception as e:
        print(f"❌ Error setting up super admin: {e}")
        if connection:
            connection.rollback()
    finally:
        if cursor:
            cursor.close()
        if connection:
            connection.close()

if __name__ == "__main__":
    create_super_admin()