from pydantic import BaseModel
from typing import Optional

class User(BaseModel):
    id: Optional[int] = None
    name: str
    email: str
    active: bool = True

class UserCreate(BaseModel):
    name: str
    email: str
    phone: str | None = None
    cell_phone: str | None = None
    active: bool = True

class AuthUser(BaseModel):
    id: Optional[int] = None
    first_name: str
    last_name: str
    email: str
    role: str
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
