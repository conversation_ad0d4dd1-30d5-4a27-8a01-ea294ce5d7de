/* Import Professional Design System */
@import './styles/design-system.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Accessibility - Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for better keyboard navigation */
.focus-visible:focus {
  outline: 2px solid #1890ff;
  outline-offset: 2px;
}

/* Ensure modal inputs maintain focus properly */
.ant-modal .ant-input:focus,
.ant-modal .ant-select-focused .ant-select-selector,
.ant-modal .ant-input-number:focus {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
  outline: none !important;
}

/* Prevent focus loss on modal inputs */
.ant-modal .ant-input,
.ant-modal .ant-select-selector,
.ant-modal .ant-input-number {
  transition: border-color 0.3s, box-shadow 0.3s;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .ant-btn {
    border-width: 2px;
  }

  .ant-card {
    border-width: 2px;
  }
}

/* Custom Main Navigation Bar Styles (Overrides for Ant Design) */
.top-navigation {
  background: #fff;
  /* border-bottom: 1px solid #e5e7eb; */
  padding: 0;
}
.top-navigation .ant-menu-horizontal {
  position: relative;
  min-height: 48px;
  align-items: flex-end;
  border-bottom: none !important;
  background: transparent !important;
}
.top-navigation .ant-menu-horizontal > .ant-menu-item {
  position: relative;
  border: 1px solid #d4d4d4 !important;
  border-bottom: none !important;
  border-radius: 4px 4px 0 0 !important;
  margin-right: 8px !important;
  background: #fff !important;
  color: #2563eb !important;
  font-size: 16px;
  font-weight: 400;
  height: 48px !important;
  line-height: 48px !important;
  min-width: 120px;
  text-align: center;
  transition: color 0.2s, background 0.2s, border 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  z-index: 2;
}
.top-navigation .ant-menu-horizontal > .ant-menu-item-selected {
  background: #fff !important;
  color: #111 !important;
  font-weight: 600;
  border-left: 2.5px solid #222 !important;
  border-right: 2.5px solid #222 !important;
  border-top: 2.5px solid #222 !important;
  border-bottom: none !important;
  z-index: 3;
}
.top-navigation .ant-menu-horizontal > .ant-menu-item:not(.ant-menu-item-selected):hover {
  color: #fff !important;
  background: #2563eb !important;
  border-color: #2563eb !important;
}
.top-navigation .ant-menu-horizontal > .ant-menu-item .anticon {
  display: none !important;
}
.top-navigation .ant-menu-horizontal > .ant-menu-item:last-child {
  margin-right: 0 !important;
}
/* Remove menu overflow indicator */
.top-navigation .ant-menu-overflow-item-rest {
  display: none !important;
}

/* Nav bar wrapper for custom underline and left indent */
.nav-bar-wrapper {
  position: relative;
  background: #fff;
  /* The left margin is set inline for now, but you can move it here if you want a fixed value */
}

/* Custom underline that starts under the first tab and ends under the last tab */
.custom-nav-underline {
  position: absolute;
  left: 0;
  top: 49px;
  height: 2px;
  width: calc(8 * 120px + 7 * 8px);
  background: #222;
  z-index: 1;
  pointer-events: none;
}

/* Hide underline under the selected tab using a mask effect */
.top-navigation .ant-menu-horizontal > .ant-menu-item-selected {
  box-shadow: 0 2px 0 0 #fff;
}

/* Adjust vertical separator between logo and title if needed */
.logo-container + .h-10 {
  border-left: 2px solid #d4d4d4 !important;
  margin-left: 16px;
  margin-right: 16px;
}

/* --- Custom Table Design for Professional Minimal Look --- */
.ant-table-wrapper {
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  box-shadow: none;
}

.ant-table {
  background: #fff;
  border-radius: 6px;
  border: none;
}

.ant-table-thead > tr > th {
  background: #f5f6fa !important;
  color: #222 !important;
  font-weight: 600 !important;
  border-bottom: 1.5px solid #e5e7eb !important;
  padding: 10px 14px !important;
  text-align: left !important;
  font-size: 14px;
}

.ant-table-tbody > tr > td {
  background: #fff !important;
  color: #222;
  border-bottom: 1px solid #f0f0f0 !important;
  padding: 10px 14px !important;
  text-align: left !important;
  font-size: 14px;
  vertical-align: middle;
}

.ant-table-tbody > tr {
  height: 44px;
  transition: background 0.2s;
}

/* Selected row underline */
.ant-table-tbody > tr.ant-table-row-selected > td {
  border-bottom: 2px solid #2563eb !important;
  background: #f8fafc !important;
}

/* Row hover effect */
.ant-table-tbody > tr:hover > td {
  background: #f5f6fa !important;
}

/* Table border */
.ant-table-container {
  border-radius: 6px;
  border: none;
}

/* Remove extra spacing above/below table */
.ant-table-content {
  margin: 0 !important;
}


/* Restore and enhance vertical column lines for header and body */
.ant-table-thead > tr > th {
  border-right: 1px solid #e5e7eb !important;
}
.ant-table-thead > tr > th:last-child {
  border-right: none !important;
}

.ant-table-tbody > tr > td {
  border-right: 1px solid #f0f0f0 !important;
}
.ant-table-tbody > tr > td:last-child {
  border-right: none !important;
}

/* Ensure no double border or extra space between header and first row */
.ant-table-thead > tr > th {
  border-bottom-width: 1.5px !important;
}
.ant-table-tbody > tr > td {
  border-top: none !important;
}
