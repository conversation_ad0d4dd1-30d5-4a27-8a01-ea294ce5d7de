#!/usr/bin/env python3
"""
Script to verify and generate correct password hash.
"""

import bcrypt
import psycopg2
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'database': os.getenv('DB_NAME', 'postgres'),
    'user': os.getenv('DB_USER', 'postgres'),
    'password': os.getenv('DB_PASSWORD', ''),
    'port': int(os.getenv('DB_PORT', 5432))
}

def test_password_hash():
    """Test and generate correct password hash."""
    
    # Test the current hash in database
    current_hash = "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPDStnPem"
    test_password = "superadmin123"
    
    print("🔍 Testing current hash...")
    print(f"Password: {test_password}")
    print(f"Hash: {current_hash}")
    
    # Test if current hash works
    try:
        is_valid = bcrypt.checkpw(test_password.encode(), current_hash.encode())
        print(f"Current hash valid: {is_valid}")
    except Exception as e:
        print(f"Error testing current hash: {e}")
    
    # Generate a new correct hash
    print("\n🔧 Generating new hash...")
    new_hash = bcrypt.hashpw(test_password.encode(), bcrypt.gensalt()).decode()
    print(f"New hash: {new_hash}")
    
    # Verify the new hash works
    is_new_valid = bcrypt.checkpw(test_password.encode(), new_hash.encode())
    print(f"New hash valid: {is_new_valid}")
    
    if is_new_valid:
        print(f"\n✅ SQL to update database:")
        print(f"UPDATE auth_users SET password_hash = '{new_hash}', updated_at = NOW() WHERE role = 'super_admin';")
        
        # Also update the database directly
        try:
            print("\n🔄 Updating database...")
            connection = psycopg2.connect(**DB_CONFIG)
            cursor = connection.cursor()
            
            cursor.execute(
                "UPDATE auth_users SET password_hash = %s, updated_at = NOW() WHERE role = 'super_admin'",
                (new_hash,)
            )
            connection.commit()
            print("✅ Database updated successfully!")
            
            # Verify the update
            cursor.execute("SELECT email, role FROM auth_users WHERE role = 'super_admin'")
            user = cursor.fetchone()
            if user:
                print(f"✅ Super admin found: {user[0]} ({user[1]})")
                print(f"✅ You can now login with:")
                print(f"   Email: {user[0]}")
                print(f"   Password: {test_password}")
            
        except Exception as e:
            print(f"❌ Database update failed: {e}")
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()

if __name__ == "__main__":
    test_password_hash()