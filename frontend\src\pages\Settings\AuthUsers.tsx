import React, { useEffect, useState } from 'react';
import { Table, Button, Modal, Form, Input, Select, message, Popconfirm, Space, Tag, Card } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import apiClient from '../../services/api-client';

interface AuthUser {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  role: string;
  created_at?: string;
  updated_at?: string;
}

const roleOptions = [
  { label: 'Super Admin', value: 'super_admin' },
  { label: 'Admin', value: 'admin' },
  { label: 'User', value: 'user' },
];

const AuthUsers: React.FC = () => {
  const [users, setUsers] = useState<AuthUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<AuthUser | null>(null);
  const [form] = Form.useForm();

  const role = localStorage.getItem('role');
  const sub = (() => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return null;
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.sub;
    } catch {
      return null;
    }
  })();

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const res = await apiClient.get('/api/auth/users');
            // Ensure we always set an array
      if (Array.isArray(res.data)) {
        setUsers(res.data);
      } else {
        console.error('API returned non-array data:', res.data);
        setUsers([]);
        message.error('Invalid data format received from server');
      }
    } catch (err: any) {
            setUsers([]); // Ensure users is always an array
      message.error(err.response?.data?.detail || 'Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const handleAdd = () => {
    setEditingUser(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (user: AuthUser) => {
    setEditingUser(user);
    form.setFieldsValue({ ...user, password: '' });
    setModalVisible(true);
  };

  const handleDelete = async (user: AuthUser) => {
    if (user.role === 'super_admin') {
      message.warning('Cannot delete super admin');
      return;
    }
    try {
      await apiClient.delete(`/api/auth/users/${user.id}`);
      message.success('User deleted');
      fetchUsers();
    } catch (err: any) {
      message.error(err.response?.data?.detail || 'Failed to delete user');
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();
      
      // Filter out empty password for updates
      const payload = { ...values };
      if (editingUser && (!payload.password || payload.password.trim() === '')) {
        delete payload.password;
      }
      
      if (editingUser) {
        // Update
        await apiClient.put(`/api/auth/users/${editingUser.id}`, payload);
        message.success('User updated');
      } else {
        // Create
        await apiClient.post('/api/auth/users', payload);
        message.success('User created');
      }
      setModalVisible(false);
      fetchUsers();
    } catch (err: any) {
      if (err.errorFields) {
        return; // Validation error
      }
      message.error(err.response?.data?.detail || 'Failed to save user');
    }
  };

  const columns: ColumnsType<AuthUser> = [
    { 
      title: 'First Name', 
      dataIndex: 'first_name', 
      key: 'first_name',
      width: 120,
    },
    { 
      title: 'Last Name', 
      dataIndex: 'last_name', 
      key: 'last_name',
      width: 120,
    },
    { 
      title: 'Email', 
      dataIndex: 'email', 
      key: 'email',
      ellipsis: true,
    },
    { 
      title: 'Role', 
      dataIndex: 'role', 
      key: 'role',
      width: 120,
      render: (role: string) => (
        <Tag color={role === 'super_admin' ? 'red' : role === 'admin' ? 'blue' : 'default'}>
          {role.replace('_', ' ').toUpperCase()}
        </Tag>
      )
    },
    { 
      title: 'Created', 
      dataIndex: 'created_at', 
      key: 'created_at',
      width: 100,
      responsive: ['md'] as const,
      render: (v: string) => v ? v.split('T')[0] : ''
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_: any, record: AuthUser) => {
        const isSuperAdmin = record.role === 'super_admin';
        const canEditSuperAdmin = isSuperAdmin && role === 'super_admin' && String(record.id) === String(sub);
        return (
          <Space size="small">
            {(!isSuperAdmin || canEditSuperAdmin) && (
              <Button type="link" size="small" onClick={() => handleEdit(record)}>
                Edit
              </Button>
            )}
            <Popconfirm 
              title="Delete this user?" 
              onConfirm={() => handleDelete(record)} 
              disabled={isSuperAdmin}
            >
              <Button type="link" size="small" danger disabled={isSuperAdmin}>
                Delete
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <div>
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
        <h2 className="text-2xl font-semibold">Auth User Management</h2>
        <Button type="primary" onClick={handleAdd} className="w-full sm:w-auto">
          Add User
        </Button>
      </div>

      <Card>
        <div className="w-full overflow-x-auto">
          <Table 
            rowKey="id" 
            columns={columns} 
            dataSource={users} 
            loading={loading} 
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              position: ['bottomRight'],
              showTotal: (total) => `${total} items`,
              showLessItems: true,
            }}
            size="middle"
            scroll={{ x: 'max-content' }}
          />
        </div>
      </Card>

      <Modal
        title={editingUser ? 'Edit User' : 'Add User'}
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => setModalVisible(false)}
        destroyOnClose
        width={500}
        style={{ top: 20 }}
        bodyStyle={{ maxHeight: 'calc(100vh - 200px)', overflowY: 'auto' }}
      >
        <Form form={form} layout="vertical" initialValues={{ role: 'user' }}>
          <Form.Item 
            name="first_name" 
            label="First Name" 
            rules={[{ required: true, message: 'Please enter first name' }]}
          > 
            <Input placeholder="Enter first name" /> 
          </Form.Item>
          <Form.Item 
            name="last_name" 
            label="Last Name" 
            rules={[{ required: true, message: 'Please enter last name' }]}
          > 
            <Input placeholder="Enter last name" /> 
          </Form.Item>
          <Form.Item 
            name="email" 
            label="Email" 
            rules={[
              { required: true, message: 'Please enter email' },
              { type: 'email', message: 'Please enter a valid email' }
            ]}
          > 
            <Input placeholder="Enter email address" /> 
          </Form.Item>
          <Form.Item 
            name="role" 
            label="Role" 
            rules={[{ required: true, message: 'Please select a role' }]}
          > 
            <Select 
              options={roleOptions} 
              disabled={editingUser?.role === 'super_admin'}
              placeholder="Select role"
            /> 
          </Form.Item>
          <Form.Item 
            name="password" 
            label="Password" 
            rules={editingUser ? [] : [{ required: true, message: 'Please enter password' }]}
          > 
            <Input.Password 
              autoComplete="new-password" 
              placeholder={editingUser ? "Leave blank to keep current password" : "Enter password"}
            /> 
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AuthUsers; 